// Cloudflare Worker to fetch Bible passages from BibleGateway.com
// Optimized for Kindle Paperwhite 7th gen (WebKit 534, ES5 only)



export default {
    async fetch(request) {
        try {
            // Parse the request URL to get translation, book, and chapter
            var url = new URL(request.url);
            var pathParts = url.pathname.split('/').filter(function(p) { return p; });

            if (pathParts.length < 3) {
                return new Response(
                    'Usage: /{Translation}/{Book}/{Chapter}\nExample: /NIV/John/1',
                    { status: 400, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            var translation = pathParts[0];
            var book = pathParts[1];
            var chapter = pathParts[2];
            var passageRef = book + ' ' + chapter;

            // Construct BibleGateway URL
            var bibleGatewayUrl = 'https://www.biblegateway.com/passage/?search=' +
                encodeURIComponent(passageRef) + '&version=' + encodeURIComponent(translation);

            // Fetch the page from BibleGateway
            var response = await fetch(bibleGatewayUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (X11; U; Linux armv7l like Android; en-us) AppleWebKit/531.2 (KHTML, like Gecko) Version/5.0 Safari/533.2 Kindle/3.0'
                }
            });

            if (!response.ok) {
                return new Response(
                    'Failed to fetch passage: ' + response.status + ' ' + response.statusText,
                    { status: 500, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            // Get the raw HTML first to extract div.std-text content
            var htmlText = await response.text();

            // Extract the div.std-text content using regex
            var stdTextMatch = htmlText.match(/<div[^>]*class="std-text"[^>]*>([\s\S]*?)<\/div>/);
            if (!stdTextMatch) {
                return new Response(
                    'Could not find passage content for "' + passageRef + '" in ' + translation +
                    '. The passage may not exist or BibleGateway structure changed.',
                    { status: 404, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            // Extract the full div.std-text element (including the div tags)
            var stdTextContent = stdTextMatch[0];

            // Extract title from h1.bcv
            var titleMatch = htmlText.match(/<h1[^>]*class="[^"]*bcv[^"]*"[^>]*>([\s\S]*?)<\/h1>/);
            var title = '';
            if (titleMatch) {
                // Remove HTML tags from title
                title = titleMatch[1].replace(/<[^>]*>/g, '').trim();
            }

            // Clean up title
            var cleanTitle = title || (passageRef + ' - ' + translation);

            // Generate Kindle-optimized HTML
            var html = '<!DOCTYPE html>\n' +
                '<html lang="en">\n' +
                '<head>\n' +
                '    <meta charset="UTF-8">\n' +
                '    <title>' + cleanTitle + '</title>\n' +
                '    <style>\n' +
                '        /* Kindle Paperwhite 7th gen optimizations */\n' +
                '        body {\n' +
                '            width: 100%; /* Required for clientWidth to work */\n' +
                '            font-family: serif;\n' +
                '            font-size: 1.8rem; /* Large text for e-ink readability */\n' +
                '            line-height: 1.7;\n' +
                '            margin: 0;\n' +
                '            padding: 2rem;\n' +
                '            background-color: white;\n' +
                '            color: black;\n' +
                '            /* Hide scrollbar using nested container trick */\n' +
                '            overflow: hidden;\n' +
                '        }\n' +
                '        .content-wrapper {\n' +
                '            width: 103%;\n' +
                '            height: 100vh;\n' +
                '            overflow-y: auto;\n' +
                '            padding-right: 3%;\n' +
                '            margin-right: -3%;\n' +
                '        }\n' +
                '        .inner-content {\n' +
                '            max-width: 1000px;\n' +
                '            margin: 0 auto;\n' +
                '        }\n' +
                '        h1 {\n' +
                '            font-size: 2.2rem;\n' +
                '            text-align: center;\n' +
                '            margin-bottom: 2rem;\n' +
                '            font-weight: normal;\n' +
                '        }\n' +
                '        /* Preserve BibleGateway styling but optimize for Kindle */\n' +
                '        .std-text {\n' +
                '            font-size: inherit;\n' +
                '            line-height: inherit;\n' +
                '        }\n' +
                '        .std-text p {\n' +
                '            margin-bottom: 1.2rem;\n' +
                '        }\n' +
                '        /* Style verse numbers if present */\n' +
                '        .versenum {\n' +
                '            font-size: 0.8rem;\n' +
                '            vertical-align: super;\n' +
                '            color: #666;\n' +
                '        }\n' +
                '        /* Style cross-references and footnotes if present */\n' +
                '        .crossreference, .footnote {\n' +
                '            font-size: 0.7rem;\n' +
                '            vertical-align: super;\n' +
                '            color: #999;\n' +
                '        }\n' +
                '        /* Remove link underlines using data-href workaround */\n' +
                '        a {\n' +
                '            color: inherit;\n' +
                '            text-decoration: none;\n' +
                '        }\n' +
                '        /* Float-based layout for compatibility */\n' +
                '        .float-left { float: left; }\n' +
                '        .float-right { float: right; }\n' +
                '        .clearfix:after {\n' +
                '            content: "";\n' +
                '            display: table;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '        .footer {\n' +
                '            margin-top: 3rem;\n' +
                '            text-align: center;\n' +
                '            font-size: 1rem;\n' +
                '            color: #666;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '    </style>\n' +
                '</head>\n' +
                '<body>\n' +
                '    <div class="content-wrapper">\n' +
                '        <div class="inner-content">\n' +
                '            <h1>' + cleanTitle + '</h1>\n' +
                '            ' + stdTextContent + '\n' +
                '            <div class="footer">\n' +
                '                <p>' + translation.toUpperCase() + ' - BibleGateway.com</p>\n' +
                '            </div>\n' +
                '        </div>\n' +
                '    </div>\n' +
                '</body>\n' +
                '</html>';

            return new Response(html, {
                headers: { 'Content-Type': 'text/html; charset=utf-8' }
            });

        } catch (error) {
            return new Response(
                'Internal server error: ' + error.message,
                { status: 500, headers: { 'Content-Type': 'text/plain' } }
            );
        }
    }
};