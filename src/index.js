// Cloudflare Worker to fetch Bible passages from BibleGateway.com
// Optimized for Kindle Paperwhite 7th gen (WebKit 534, ES5 only)

// Simple HTML content extractor that preserves the original div.std-text structure
class PassageExtractor {
    constructor() {
        this.stdTextContent = '';
        this.title = '';
        this.isInStdText = false;
        this.titleFound = false;
        this.elementStack = [];
    }

    element(element) {
        var self = this;

        // Capture the title from the passage display
        if (element.tagName === 'h1' && element.getAttribute('class') &&
            element.getAttribute('class').indexOf('bcv') !== -1) {
            return;
        }

        // Look for the div.std-text container
        if (element.tagName === 'div' && element.getAttribute('class') === 'std-text') {
            this.isInStdText = true;
            // Start capturing the div.std-text with all its attributes
            var attrs = '';
            var attributeNames = ['class', 'id', 'data-version', 'data-passage'];
            for (var i = 0; i < attributeNames.length; i++) {
                var attrName = attributeNames[i];
                var attrValue = element.getAttribute(attrName);
                if (attrValue) {
                    attrs += ' ' + attrName + '="' + attrValue.replace(/"/g, '&quot;') + '"';
                }
            }
            this.stdTextContent += '<div' + attrs + '>';

            element.onEndTag(function() {
                self.stdTextContent += '</div>';
                self.isInStdText = false;
            });
            return;
        }

        // If we're inside div.std-text, capture everything
        if (this.isInStdText) {
            this.captureElement(element);
        }
    }

    captureElement(element) {
        var self = this;

        // Build opening tag with attributes
        var attrs = '';
        var commonAttrs = ['class', 'id', 'data-usfm', 'data-osis', 'style'];
        for (var i = 0; i < commonAttrs.length; i++) {
            var attrName = commonAttrs[i];
            var attrValue = element.getAttribute(attrName);
            if (attrValue) {
                attrs += ' ' + attrName + '="' + attrValue.replace(/"/g, '&quot;') + '"';
            }
        }

        var tagName = element.tagName.toLowerCase();
        this.stdTextContent += '<' + tagName + attrs + '>';

        // Handle void elements (self-closing)
        var voidElements = ['br', 'hr', 'img', 'input', 'meta', 'link'];
        var isVoid = false;
        for (var i = 0; i < voidElements.length; i++) {
            if (tagName === voidElements[i]) {
                isVoid = true;
                break;
            }
        }

        if (!isVoid) {
            element.onEndTag(function() {
                self.stdTextContent += '</' + tagName + '>';
            });
        }
    }

    text(text) {
        if (text && text.text) {
            if (this.isInStdText) {
                this.stdTextContent += text.text;
            } else if (!this.titleFound && text.text.trim()) {
                // Capture title text
                this.title += text.text.trim() + ' ';
            }
        }
    }

    comments(comment) {
        if (this.isInStdText && comment && comment.text) {
            this.stdTextContent += '<!--' + comment.text + '-->';
        }
    }
}

export default {
    async fetch(request) {
        try {
            // Parse the request URL to get translation, book, and chapter
            var url = new URL(request.url);
            var pathParts = url.pathname.split('/').filter(function(p) { return p; });

            if (pathParts.length < 3) {
                return new Response(
                    'Usage: /{Translation}/{Book}/{Chapter}\nExample: /NIV/John/1',
                    { status: 400, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            var translation = pathParts[0];
            var book = pathParts[1];
            var chapter = pathParts[2];
            var passageRef = book + ' ' + chapter;

            // Construct BibleGateway URL
            var bibleGatewayUrl = 'https://www.biblegateway.com/passage/?search=' +
                encodeURIComponent(passageRef) + '&version=' + encodeURIComponent(translation);

            // Fetch the page from BibleGateway
            var response = await fetch(bibleGatewayUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (X11; U; Linux armv7l like Android; en-us) AppleWebKit/531.2 (KHTML, like Gecko) Version/5.0 Safari/533.2 Kindle/3.0'
                }
            });

            if (!response.ok) {
                return new Response(
                    'Failed to fetch passage: ' + response.status + ' ' + response.statusText,
                    { status: 500, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            // Extract the passage content using HTMLRewriter
            var extractor = new PassageExtractor();
            var rewriter = new HTMLRewriter()
                .on('*', extractor);

            var transformedResponse = rewriter.transform(response);
            await transformedResponse.arrayBuffer(); // Consume the stream to trigger handlers

            // Validate that we got content
            if (!extractor.stdTextContent) {
                return new Response(
                    'Could not find passage content for "' + passageRef + '" in ' + translation +
                    '. The passage may not exist or BibleGateway structure changed.',
                    { status: 404, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            // Clean up title
            var cleanTitle = extractor.title.trim() || (passageRef + ' - ' + translation);

            // Generate Kindle-optimized HTML
            var html = '<!DOCTYPE html>\n' +
                '<html lang="en">\n' +
                '<head>\n' +
                '    <meta charset="UTF-8">\n' +
                '    <title>' + cleanTitle + '</title>\n' +
                '    <style>\n' +
                '        /* Kindle Paperwhite 7th gen optimizations */\n' +
                '        body {\n' +
                '            width: 100%; /* Required for clientWidth to work */\n' +
                '            font-family: serif;\n' +
                '            font-size: 1.8rem; /* Large text for e-ink readability */\n' +
                '            line-height: 1.7;\n' +
                '            margin: 0;\n' +
                '            padding: 2rem;\n' +
                '            background-color: white;\n' +
                '            color: black;\n' +
                '            /* Hide scrollbar using nested container trick */\n' +
                '            overflow: hidden;\n' +
                '        }\n' +
                '        .content-wrapper {\n' +
                '            width: 103%;\n' +
                '            height: 100vh;\n' +
                '            overflow-y: auto;\n' +
                '            padding-right: 3%;\n' +
                '            margin-right: -3%;\n' +
                '        }\n' +
                '        .inner-content {\n' +
                '            max-width: 1000px;\n' +
                '            margin: 0 auto;\n' +
                '        }\n' +
                '        h1 {\n' +
                '            font-size: 2.2rem;\n' +
                '            text-align: center;\n' +
                '            margin-bottom: 2rem;\n' +
                '            font-weight: normal;\n' +
                '        }\n' +
                '        /* Preserve BibleGateway styling but optimize for Kindle */\n' +
                '        .std-text {\n' +
                '            font-size: inherit;\n' +
                '            line-height: inherit;\n' +
                '        }\n' +
                '        .std-text p {\n' +
                '            margin-bottom: 1.2rem;\n' +
                '        }\n' +
                '        /* Style verse numbers if present */\n' +
                '        .versenum {\n' +
                '            font-size: 0.8rem;\n' +
                '            vertical-align: super;\n' +
                '            color: #666;\n' +
                '        }\n' +
                '        /* Style cross-references and footnotes if present */\n' +
                '        .crossreference, .footnote {\n' +
                '            font-size: 0.7rem;\n' +
                '            vertical-align: super;\n' +
                '            color: #999;\n' +
                '        }\n' +
                '        /* Remove link underlines using data-href workaround */\n' +
                '        a {\n' +
                '            color: inherit;\n' +
                '            text-decoration: none;\n' +
                '        }\n' +
                '        /* Float-based layout for compatibility */\n' +
                '        .float-left { float: left; }\n' +
                '        .float-right { float: right; }\n' +
                '        .clearfix:after {\n' +
                '            content: "";\n' +
                '            display: table;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '        .footer {\n' +
                '            margin-top: 3rem;\n' +
                '            text-align: center;\n' +
                '            font-size: 1rem;\n' +
                '            color: #666;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '    </style>\n' +
                '</head>\n' +
                '<body>\n' +
                '    <div class="content-wrapper">\n' +
                '        <div class="inner-content">\n' +
                '            <h1>' + cleanTitle + '</h1>\n' +
                '            ' + extractor.stdTextContent + '\n' +
                '            <div class="footer">\n' +
                '                <p>' + translation.toUpperCase() + ' - BibleGateway.com</p>\n' +
                '            </div>\n' +
                '        </div>\n' +
                '    </div>\n' +
                '</body>\n' +
                '</html>';

            return new Response(html, {
                headers: { 'Content-Type': 'text/html; charset=utf-8' }
            });

        } catch (error) {
            return new Response(
                'Internal server error: ' + error.message,
                { status: 500, headers: { 'Content-Type': 'text/plain' } }
            );
        }
    }
};